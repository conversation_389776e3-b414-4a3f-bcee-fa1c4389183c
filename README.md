# IoT网关协议服务器Demo

这是一个支持多种工业协议的模拟服务端demo，可以配置启动不同的协议服务器，持续发送模拟数据。

## 支持的协议

- **OPC UA** - OPC统一架构协议
- **OPC DA** - OPC数据访问协议（模拟实现）
- **Modbus TCP** - Modbus TCP协议
- **Modbus RTU** - Modbus RTU协议

## 功能特性

- 🚀 支持多种工业协议
- 📊 实时数据模拟（温度、压力、流量、电机状态、计数器）
- ⚙️ 灵活的配置管理
- 🎛️ 交互式控制台
- 📝 完整的日志记录
- 🔄 服务器热重启

## 快速开始

### 1. 编译项目

```bash
mvn clean compile
```

### 2. 运行服务器

#### 使用默认配置（OPC UA）
```bash
mvn exec:java -Dexec.mainClass="com.nimblex.iot.Main"
```

#### 指定协议类型
```bash
# 启动OPC UA服务器
mvn exec:java -Dexec.mainClass="com.nimblex.iot.Main" -Dexec.args="OPC_UA"

# 启动Modbus TCP服务器
mvn exec:java -Dexec.mainClass="com.nimblex.iot.Main" -Dexec.args="MODBUS_TCP"

# 启动Modbus RTU服务器
mvn exec:java -Dexec.mainClass="com.nimblex.iot.Main" -Dexec.args="MODBUS_RTU"

# 启动OPC DA服务器
mvn exec:java -Dexec.mainClass="com.nimblex.iot.Main" -Dexec.args="OPC_DA"
```

### 3. 控制台命令

服务器启动后，可以使用以下命令：

- `status` - 显示服务器状态
- `info` - 显示服务器详细信息
- `restart` - 重启服务器
- `stop` - 停止服务器
- `help` - 显示帮助信息
- `quit` - 退出程序

## 配置说明

配置文件位于 `src/main/resources/application.properties`

### 协议选择
```properties
# 支持的协议类型: OPC_UA, MODBUS_TCP, MODBUS_RTU, OPC_DA
protocol.type=OPC_UA
```

### OPC UA配置
```properties
opcua.server.port=4840
opcua.server.endpoint=opc.tcp://localhost:4840/iot-gateway
opcua.server.name=IoT Gateway OPC UA Server
opcua.data.update.interval=1000
```

### Modbus TCP配置
```properties
modbus.tcp.port=502
modbus.tcp.host=localhost
modbus.tcp.unit.id=1
modbus.tcp.data.update.interval=1000
```

### Modbus RTU配置
```properties
modbus.rtu.port=COM1
modbus.rtu.baudrate=9600
modbus.rtu.databits=8
modbus.rtu.stopbits=1
modbus.rtu.parity=NONE
modbus.rtu.unit.id=1
modbus.rtu.data.update.interval=1000
```

### 数据模拟配置
```properties
data.simulation.enabled=true
data.simulation.temperature.min=20.0
data.simulation.temperature.max=80.0
data.simulation.pressure.min=1.0
data.simulation.pressure.max=10.0
data.simulation.flow.min=0.0
data.simulation.flow.max=100.0
```

## 模拟数据

服务器会持续生成以下模拟数据：

| 数据项 | 类型 | 范围 | 单位 | 描述 |
|--------|------|------|------|------|
| 温度 | Double | 20.0 - 80.0 | °C | 温度传感器数据 |
| 压力 | Double | 1.0 - 10.0 | bar | 压力传感器数据 |
| 流量 | Double | 0.0 - 100.0 | L/min | 流量传感器数据 |
| 电机状态 | Boolean | true/false | - | 电机运行状态 |
| 计数器 | Integer | 递增 | 个 | 生产计数器 |

## 客户端连接

### OPC UA客户端连接
- **端点**: `opc.tcp://localhost:4840/iot-gateway`
- **安全策略**: None（无安全）
- **用户认证**: 
  - 用户名: `user`, 密码: `password1`
  - 用户名: `admin`, 密码: `password2`

### Modbus TCP客户端连接
- **地址**: `localhost:502`
- **单元ID**: 1
- **寄存器映射**:
  - 地址0: 温度 × 10
  - 地址1: 压力 × 100
  - 地址2: 流量
  - 地址3: 计数器低位
  - 地址4: 计数器高位

### Modbus RTU客户端连接
- **串口**: COM1（或配置的串口）
- **波特率**: 9600
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **单元ID**: 1

## 日志文件

日志文件保存在 `logs/` 目录下：

- `iot-gateway.log` - 主日志文件
- `iot-gateway-error.log` - 错误日志文件

## 项目结构

```
src/main/java/com/nimblex/iot/
├── Main.java                           # 主启动类
├── config/
│   └── ConfigManager.java              # 配置管理器
├── data/
│   └── DataSimulator.java              # 数据模拟器
└── protocol/
    ├── ProtocolServer.java             # 协议服务器接口
    ├── ProtocolType.java               # 协议类型枚举
    ├── ProtocolServerFactory.java      # 协议服务器工厂
    ├── opcua/
    │   ├── OpcUaServer.java            # OPC UA服务器实现
    │   └── OpcUaNamespace.java         # OPC UA命名空间
    ├── opcda/
    │   └── OpcDaServer.java            # OPC DA服务器实现（模拟）
    └── modbus/
        ├── ModbusTcpServer.java        # Modbus TCP服务器实现
        └── ModbusRtuServer.java        # Modbus RTU服务器实现
```

## 依赖项

主要依赖项包括：

- Eclipse Milo (OPC UA)
- Modbus库 (Modbus TCP/RTU)
- jSerialComm (串口通信)
- SLF4J + Logback (日志)
- Jackson (JSON处理)

## 注意事项

1. **OPC DA**: 由于OPC DA需要Windows COM组件支持，当前实现为模拟版本
2. **Modbus RTU**: 如果没有可用的串口，会启动虚拟模式进行演示
3. **防火墙**: 确保相应的端口（4840, 502等）没有被防火墙阻止
4. **权限**: Modbus TCP使用502端口可能需要管理员权限

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改配置文件中的端口号
   - 或者停止占用端口的其他程序

2. **串口无法打开**
   - 检查串口名称是否正确
   - 确保串口没有被其他程序占用
   - 在Windows上可能需要安装串口驱动

3. **OPC UA连接失败**
   - 检查防火墙设置
   - 确认端点地址正确
   - 检查用户名密码

## 扩展开发

要添加新的协议支持：

1. 在 `ProtocolType` 枚举中添加新协议类型
2. 实现 `ProtocolServer` 接口
3. 在 `ProtocolServerFactory` 中添加创建逻辑
4. 在配置文件中添加相应配置项

## 许可证

本项目仅用于学习和演示目的。
