package com.nimblex.iot;

import com.nimblex.iot.config.ConfigManager;
import com.nimblex.iot.protocol.ProtocolServer;
import com.nimblex.iot.protocol.ProtocolServerFactory;
import com.nimblex.iot.protocol.ProtocolType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;

/**
 * IoT网关协议服务器主启动类
 * 支持OPC UA、OPC DA、Modbus TCP、Modbus RTU等协议
 */
public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    private static ProtocolServer protocolServer;
    private static final Scanner scanner = new Scanner(System.in);

    public static void main(String[] args) {
        logger.info("========================================");
        logger.info("    IoT网关协议服务器启动中...");
        logger.info("========================================");

        try {
            // 加载配置
            ConfigManager configManager = new ConfigManager();
            configManager.printConfiguration();

            // 如果有命令行参数，允许覆盖协议类型
            ProtocolType protocolType = configManager.getProtocolType();
            if (args.length > 0) {
                try {
                    protocolType = ProtocolType.valueOf(args[0].toUpperCase());
                    logger.info("通过命令行参数覆盖协议类型: {}", protocolType);
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的命令行协议类型参数: {}, 使用配置文件中的设置", args[0]);
                }
            }

            // 显示支持的协议类型
            showSupportedProtocols();

            // 创建协议服务器
            protocolServer = ProtocolServerFactory.createServer(protocolType, configManager.getProperties());

            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("接收到关闭信号，正在停止服务器...");
                stopServer();
            }));

            // 启动服务器
            startServer();

            // 显示控制菜单
            showControlMenu();

        } catch (Exception e) {
            logger.error("启动服务器时发生错误", e);
            System.exit(1);
        }
    }

    /**
     * 显示支持的协议类型
     */
    private static void showSupportedProtocols() {
        logger.info("支持的协议类型:");
        for (ProtocolType type : ProtocolType.values()) {
            logger.info("  - {}", type.toString());
        }
        logger.info("");
    }

    /**
     * 启动服务器
     */
    private static void startServer() {
        try {
            logger.info("正在启动协议服务器: {}", protocolServer.getProtocolType());
            protocolServer.start();

            logger.info("========================================");
            logger.info("  🚀 服务器启动成功！");
            logger.info("  📡 协议类型: {}", protocolServer.getProtocolType().getName());
            logger.info("  ℹ️  服务器信息: {}", protocolServer.getServerInfo());
            logger.info("========================================");

        } catch (Exception e) {
            logger.error("启动服务器失败", e);
            throw new RuntimeException("服务器启动失败", e);
        }
    }

    /**
     * 停止服务器
     */
    private static void stopServer() {
        if (protocolServer != null && protocolServer.isRunning()) {
            try {
                protocolServer.stop();
                logger.info("服务器已成功停止");
            } catch (Exception e) {
                logger.error("停止服务器时发生错误", e);
            }
        }
    }

    /**
     * 显示控制菜单
     */
    private static void showControlMenu() {
        logger.info("");
        logger.info("控制命令:");
        logger.info("  status  - 显示服务器状态");
        logger.info("  info    - 显示服务器详细信息");
        logger.info("  restart - 重启服务器");
        logger.info("  stop    - 停止服务器");
        logger.info("  help    - 显示帮助信息");
        logger.info("  quit    - 退出程序");
        logger.info("");

        // 命令循环
        while (true) {
            System.out.print("IoT-Gateway> ");
            String command = scanner.nextLine().trim().toLowerCase();

            switch (command) {
                case "status":
                    showServerStatus();
                    break;

                case "info":
                    showServerInfo();
                    break;

                case "restart":
                    restartServer();
                    break;

                case "stop":
                    stopServer();
                    return;

                case "help":
                    showControlMenu();
                    break;

                case "quit":
                case "exit":
                    logger.info("正在退出程序...");
                    stopServer();
                    System.exit(0);
                    break;

                case "":
                    // 空命令，忽略
                    break;

                default:
                    logger.warn("未知命令: {}，输入 'help' 查看可用命令", command);
                    break;
            }
        }
    }

    /**
     * 显示服务器状态
     */
    private static void showServerStatus() {
        if (protocolServer != null) {
            logger.info("服务器状态: {}", protocolServer.isRunning() ? "运行中" : "已停止");
            logger.info("协议类型: {}", protocolServer.getProtocolType().getName());
        } else {
            logger.info("服务器状态: 未初始化");
        }
    }

    /**
     * 显示服务器详细信息
     */
    private static void showServerInfo() {
        if (protocolServer != null) {
            logger.info("========================================");
            logger.info("服务器详细信息:");
            logger.info("  状态: {}", protocolServer.isRunning() ? "运行中" : "已停止");
            logger.info("  协议: {}", protocolServer.getProtocolType().toString());
            logger.info("  信息: {}", protocolServer.getServerInfo());
            logger.info("  运行时间: {}ms", System.currentTimeMillis());
            logger.info("========================================");
        } else {
            logger.info("服务器未初始化");
        }
    }

    /**
     * 重启服务器
     */
    private static void restartServer() {
        logger.info("正在重启服务器...");
        try {
            if (protocolServer != null && protocolServer.isRunning()) {
                protocolServer.stop();
                Thread.sleep(2000); // 等待2秒
            }

            if (protocolServer != null) {
                protocolServer.start();
                logger.info("服务器重启成功");
            }
        } catch (Exception e) {
            logger.error("重启服务器失败", e);
        }
    }
}