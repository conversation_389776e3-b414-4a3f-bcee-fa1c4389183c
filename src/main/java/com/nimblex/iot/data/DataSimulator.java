package com.nimblex.iot.data;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 数据模拟器
 * 用于模拟工业设备的实时数据
 */
public class DataSimulator {
    
    private final Random random = new Random();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    // 模拟数据的当前值
    private final AtomicReference<Double> temperature = new AtomicReference<>(25.0);
    private final AtomicReference<Double> pressure = new AtomicReference<>(5.0);
    private final AtomicReference<Double> flow = new AtomicReference<>(50.0);
    private final AtomicReference<Boolean> motorStatus = new AtomicReference<>(false);
    private final AtomicReference<Integer> counter = new AtomicReference<>(0);
    
    // 配置参数
    private double tempMin = 20.0;
    private double tempMax = 80.0;
    private double pressureMin = 1.0;
    private double pressureMax = 10.0;
    private double flowMin = 0.0;
    private double flowMax = 100.0;
    
    private boolean running = false;
    
    /**
     * 设置温度范围
     */
    public void setTemperatureRange(double min, double max) {
        this.tempMin = min;
        this.tempMax = max;
    }
    
    /**
     * 设置压力范围
     */
    public void setPressureRange(double min, double max) {
        this.pressureMin = min;
        this.pressureMax = max;
    }
    
    /**
     * 设置流量范围
     */
    public void setFlowRange(double min, double max) {
        this.flowMin = min;
        this.flowMax = max;
    }
    
    /**
     * 启动数据模拟
     */
    public void start(long intervalMs) {
        if (running) {
            return;
        }
        
        running = true;
        scheduler.scheduleAtFixedRate(this::updateData, 0, intervalMs, TimeUnit.MILLISECONDS);
        System.out.println("数据模拟器已启动，更新间隔: " + intervalMs + "ms");
    }
    
    /**
     * 停止数据模拟
     */
    public void stop() {
        running = false;
        scheduler.shutdown();
        System.out.println("数据模拟器已停止");
    }
    
    /**
     * 更新模拟数据
     */
    private void updateData() {
        // 温度：在范围内随机波动
        double currentTemp = temperature.get();
        double tempChange = (random.nextDouble() - 0.5) * 2.0; // -1.0 到 1.0
        double newTemp = Math.max(tempMin, Math.min(tempMax, currentTemp + tempChange));
        temperature.set(newTemp);
        
        // 压力：在范围内随机波动
        double currentPressure = pressure.get();
        double pressureChange = (random.nextDouble() - 0.5) * 0.5; // -0.25 到 0.25
        double newPressure = Math.max(pressureMin, Math.min(pressureMax, currentPressure + pressureChange));
        pressure.set(newPressure);
        
        // 流量：在范围内随机波动
        double currentFlow = flow.get();
        double flowChange = (random.nextDouble() - 0.5) * 10.0; // -5.0 到 5.0
        double newFlow = Math.max(flowMin, Math.min(flowMax, currentFlow + flowChange));
        flow.set(newFlow);
        
        // 电机状态：随机切换
        if (random.nextDouble() < 0.1) { // 10%的概率切换状态
            motorStatus.set(!motorStatus.get());
        }
        
        // 计数器：递增
        counter.set(counter.get() + 1);
    }
    
    // Getter方法
    public double getTemperature() {
        return temperature.get();
    }
    
    public double getPressure() {
        return pressure.get();
    }
    
    public double getFlow() {
        return flow.get();
    }
    
    public boolean getMotorStatus() {
        return motorStatus.get();
    }
    
    public int getCounter() {
        return counter.get();
    }
    
    public boolean isRunning() {
        return running;
    }
}
