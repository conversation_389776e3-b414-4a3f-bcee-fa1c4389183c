package com.nimblex.iot.protocol;

import com.nimblex.iot.protocol.modbus.ModbusRtuServer;
import com.nimblex.iot.protocol.modbus.ModbusTcpServer;
import com.nimblex.iot.protocol.opcda.OpcDaServer;
import com.nimblex.iot.protocol.opcua.OpcUaServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 协议服务器工厂类
 * 根据配置创建相应的协议服务器实例
 */
public class ProtocolServerFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolServerFactory.class);
    
    /**
     * 根据协议类型和配置创建协议服务器
     * 
     * @param protocolType 协议类型
     * @param properties 配置属性
     * @return 协议服务器实例
     * @throws IllegalArgumentException 如果协议类型不支持
     */
    public static ProtocolServer createServer(ProtocolType protocolType, Properties properties) {
        logger.info("创建协议服务器: {}", protocolType);
        
        switch (protocolType) {
            case OPC_UA:
                return createOpcUaServer(properties);
                
            case MODBUS_TCP:
                return createModbusTcpServer(properties);
                
            case MODBUS_RTU:
                return createModbusRtuServer(properties);
                
            case OPC_DA:
                return createOpcDaServer(properties);
                
            default:
                throw new IllegalArgumentException("不支持的协议类型: " + protocolType);
        }
    }
    
    /**
     * 创建OPC UA服务器
     */
    private static ProtocolServer createOpcUaServer(Properties properties) {
        int port = Integer.parseInt(properties.getProperty("opcua.server.port", "4840"));
        String endpoint = properties.getProperty("opcua.server.endpoint", "opc.tcp://localhost:4840/iot-gateway");
        String serverName = properties.getProperty("opcua.server.name", "IoT Gateway OPC UA Server");
        long updateInterval = Long.parseLong(properties.getProperty("opcua.data.update.interval", "1000"));
        
        logger.info("OPC UA服务器配置 - 端口: {}, 端点: {}, 更新间隔: {}ms", port, endpoint, updateInterval);
        
        return new OpcUaServer(port, endpoint, serverName, updateInterval);
    }
    
    /**
     * 创建Modbus TCP服务器
     */
    private static ProtocolServer createModbusTcpServer(Properties properties) {
        String host = properties.getProperty("modbus.tcp.host", "localhost");
        int port = Integer.parseInt(properties.getProperty("modbus.tcp.port", "502"));
        int unitId = Integer.parseInt(properties.getProperty("modbus.tcp.unit.id", "1"));
        long updateInterval = Long.parseLong(properties.getProperty("modbus.tcp.data.update.interval", "1000"));
        
        logger.info("Modbus TCP服务器配置 - 地址: {}:{}, 单元ID: {}, 更新间隔: {}ms", 
                   host, port, unitId, updateInterval);
        
        return new ModbusTcpServer(host, port, unitId, updateInterval);
    }
    
    /**
     * 创建Modbus RTU服务器
     */
    private static ProtocolServer createModbusRtuServer(Properties properties) {
        String portName = properties.getProperty("modbus.rtu.port", "COM1");
        int baudRate = Integer.parseInt(properties.getProperty("modbus.rtu.baudrate", "9600"));
        int dataBits = Integer.parseInt(properties.getProperty("modbus.rtu.databits", "8"));
        int stopBits = Integer.parseInt(properties.getProperty("modbus.rtu.stopbits", "1"));
        int parity = parseParityString(properties.getProperty("modbus.rtu.parity", "NONE"));
        int unitId = Integer.parseInt(properties.getProperty("modbus.rtu.unit.id", "1"));
        long updateInterval = Long.parseLong(properties.getProperty("modbus.rtu.data.update.interval", "1000"));
        
        logger.info("Modbus RTU服务器配置 - 串口: {}, 波特率: {}, 数据位: {}, 停止位: {}, 校验: {}, 单元ID: {}, 更新间隔: {}ms", 
                   portName, baudRate, dataBits, stopBits, 
                   properties.getProperty("modbus.rtu.parity", "NONE"), unitId, updateInterval);
        
        return new ModbusRtuServer(portName, baudRate, dataBits, stopBits, parity, unitId, updateInterval);
    }
    
    /**
     * 创建OPC DA服务器
     */
    private static ProtocolServer createOpcDaServer(Properties properties) {
        String serverName = properties.getProperty("opcda.server.name", "IoT Gateway OPC DA Server");
        String clsid = properties.getProperty("opcda.server.clsid", "{12345678-1234-1234-1234-123456789ABC}");
        long updateInterval = Long.parseLong(properties.getProperty("opcda.data.update.interval", "1000"));
        
        logger.info("OPC DA服务器配置 - 名称: {}, CLSID: {}, 更新间隔: {}ms", 
                   serverName, clsid, updateInterval);
        
        return new OpcDaServer(serverName, clsid, updateInterval);
    }
    
    /**
     * 解析校验位字符串
     */
    private static int parseParityString(String parityStr) {
        switch (parityStr.toUpperCase()) {
            case "NONE":
                return 0; // SerialPort.NO_PARITY
            case "ODD":
                return 1; // SerialPort.ODD_PARITY
            case "EVEN":
                return 2; // SerialPort.EVEN_PARITY
            case "MARK":
                return 3; // SerialPort.MARK_PARITY
            case "SPACE":
                return 4; // SerialPort.SPACE_PARITY
            default:
                logger.warn("未知的校验位设置: {}, 使用默认值 NONE", parityStr);
                return 0;
        }
    }
}
