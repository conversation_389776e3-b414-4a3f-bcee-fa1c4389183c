package com.nimblex.iot.protocol.opcua;

import com.nimblex.iot.data.DataSimulator;
import com.nimblex.iot.protocol.ProtocolServer;
import com.nimblex.iot.protocol.ProtocolType;
import org.eclipse.milo.opcua.sdk.server.OpcUaServer;
import org.eclipse.milo.opcua.sdk.server.api.config.OpcUaServerConfig;
import org.eclipse.milo.opcua.sdk.server.identity.CompositeValidator;
import org.eclipse.milo.opcua.sdk.server.identity.UsernameIdentityValidator;
import org.eclipse.milo.opcua.sdk.server.identity.X509IdentityValidator;
import org.eclipse.milo.opcua.sdk.server.namespaces.OpcUaNamespace;
import org.eclipse.milo.opcua.stack.core.application.DefaultCertificateManager;
import org.eclipse.milo.opcua.stack.core.application.DefaultCertificateValidator;
import org.eclipse.milo.opcua.stack.core.security.SecurityPolicy;
import org.eclipse.milo.opcua.stack.core.transport.TransportProfile;
import org.eclipse.milo.opcua.stack.core.types.builtin.LocalizedText;
import org.eclipse.milo.opcua.stack.core.types.structured.BuildInfo;
import org.eclipse.milo.opcua.stack.server.EndpointConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * OPC UA 服务器实现
 */
public class OpcUaServer implements ProtocolServer {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private org.eclipse.milo.opcua.sdk.server.OpcUaServer miloServer;
    private com.nimblex.iot.protocol.opcua.OpcUaNamespace namespace;
    private DataSimulator dataSimulator;

    private final int port;
    private final String endpoint;
    private final String serverName;
    private final long updateInterval;

    public OpcUaServer(int port, String endpoint, String serverName, long updateInterval) {
        this.port = port;
        this.endpoint = endpoint;
        this.serverName = serverName;
        this.updateInterval = updateInterval;
        this.dataSimulator = new DataSimulator();
    }

    @Override
    public void start() throws Exception {
        logger.info("启动OPC UA服务器...");

        // 创建服务器配置
        OpcUaServerConfig serverConfig = createServerConfig();

        // 创建服务器实例
        opcUaServer = new OpcUaServer(serverConfig);

        // 创建命名空间并注册节点
        namespace = new OpcUaNamespace(opcUaServer, dataSimulator);
        namespace.startup();

        // 启动服务器
        opcUaServer.startup().get();

        // 启动数据模拟器
        dataSimulator.start(updateInterval);

        logger.info("OPC UA服务器已启动，端点: {}", endpoint);
    }

    @Override
    public void stop() throws Exception {
        logger.info("停止OPC UA服务器...");

        if (dataSimulator != null) {
            dataSimulator.stop();
        }

        if (namespace != null) {
            namespace.shutdown();
        }

        if (opcUaServer != null) {
            opcUaServer.shutdown().get();
        }

        logger.info("OPC UA服务器已停止");
    }

    @Override
    public boolean isRunning() {
        return opcUaServer != null && opcUaServer.getServer().getState().isRunning();
    }

    @Override
    public ProtocolType getProtocolType() {
        return ProtocolType.OPC_UA;
    }

    @Override
    public String getServerInfo() {
        return String.format("OPC UA服务器 - 端点: %s, 端口: %d", endpoint, port);
    }

    /**
     * 创建服务器配置
     */
    private OpcUaServerConfig createServerConfig() throws Exception {
        Set<EndpointConfiguration> endpointConfigurations = createEndpointConfigurations();

        OpcUaServerConfig.Builder configBuilder = OpcUaServerConfig.builder()
                .setApplicationUri("urn:nimblex:iot:gateway:opcua:server")
                .setApplicationName(LocalizedText.english(serverName))
                .setEndpoints(endpointConfigurations)
                .setBuildInfo(
                        new BuildInfo(
                                "urn:nimblex:iot:gateway:opcua:server",
                                "Nimblex IoT Gateway",
                                "IoT Gateway OPC UA Server",
                                OpcUaServer.SDK_VERSION,
                                "", null))
                .setCertificateManager(new DefaultCertificateManager())
                .setCertificateValidator(new DefaultCertificateValidator(new File("security")))
                .setIdentityValidator(new CompositeValidator(
                        new UsernameIdentityValidator(
                                true,
                                authChallenge -> {
                                    String username = authChallenge.getUsername();
                                    String password = authChallenge.getPassword();

                                    boolean userOk = "user".equals(username) && "password1".equals(password);
                                    boolean adminOk = "admin".equals(username) && "password2".equals(password);

                                    return userOk || adminOk;
                                }
                        ),
                        new X509IdentityValidator()
                ));

        return configBuilder.build();
    }

    /**
     * 创建端点配置
     */
    private Set<EndpointConfiguration> createEndpointConfigurations() {
        Set<EndpointConfiguration> endpointConfigurations = new LinkedHashSet<>();

        EndpointConfiguration.Builder builder = EndpointConfiguration.newBuilder()
                .setBindAddress("localhost")
                .setBindPort(port)
                .setHostname("localhost")
                .setPath("/iot-gateway")
                .setTransportProfile(TransportProfile.TCP_UASC_UABINARY);

        // 无安全策略端点
        endpointConfigurations.add(
                builder.copy()
                        .setSecurityPolicy(SecurityPolicy.None)
                        .build()
        );

        return endpointConfigurations;
    }
}
