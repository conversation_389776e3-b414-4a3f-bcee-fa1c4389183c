package com.nimblex.iot.protocol.opcua;

import com.nimblex.iot.data.DataSimulator;
import org.eclipse.milo.opcua.sdk.core.Reference;
import org.eclipse.milo.opcua.sdk.server.OpcUaServer;
import org.eclipse.milo.opcua.sdk.server.api.DataItem;
import org.eclipse.milo.opcua.sdk.server.api.ManagedNamespaceWithLifecycle;
import org.eclipse.milo.opcua.sdk.server.api.MonitoredItem;
import org.eclipse.milo.opcua.sdk.server.nodes.UaFolderNode;
import org.eclipse.milo.opcua.sdk.server.nodes.UaVariableNode;
import org.eclipse.milo.opcua.sdk.server.util.SubscriptionModel;
import org.eclipse.milo.opcua.stack.core.Identifiers;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.builtin.LocalizedText;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.QualifiedName;
import org.eclipse.milo.opcua.stack.core.types.builtin.Variant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * OPC UA 命名空间实现
 * 负责创建和管理OPC UA节点
 */
public class OpcUaNamespace extends ManagedNamespaceWithLifecycle {
    
    private final Logger logger = LoggerFactory.getLogger(getClass());
    
    public static final String NAMESPACE_URI = "urn:nimblex:iot:gateway:opcua";
    
    private final DataSimulator dataSimulator;
    private final SubscriptionModel subscriptionModel;
    private final ScheduledExecutorService scheduledExecutor;
    
    // 变量节点
    private UaVariableNode temperatureNode;
    private UaVariableNode pressureNode;
    private UaVariableNode flowNode;
    private UaVariableNode motorStatusNode;
    private UaVariableNode counterNode;
    
    public OpcUaNamespace(OpcUaServer server, DataSimulator dataSimulator) {
        super(server, NAMESPACE_URI);
        this.dataSimulator = dataSimulator;
        this.subscriptionModel = new SubscriptionModel(server, this);
        this.scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
    }
    
    @Override
    protected void onStartup() {
        super.onStartup();
        
        // 创建设备文件夹
        NodeId devicesFolderId = newNodeId("Devices");
        UaFolderNode devicesFolder = new UaFolderNode(
                getNodeContext(),
                devicesFolderId,
                newQualifiedName("Devices"),
                LocalizedText.english("Devices")
        );
        
        getNodeManager().addNode(devicesFolder);
        
        // 添加到Objects文件夹
        devicesFolder.addReference(new Reference(
                devicesFolderId,
                Identifiers.Organizes,
                Identifiers.ObjectsFolder.expanded(),
                false
        ));
        
        // 创建传感器文件夹
        NodeId sensorsFolderId = newNodeId("Sensors");
        UaFolderNode sensorsFolder = new UaFolderNode(
                getNodeContext(),
                sensorsFolderId,
                newQualifiedName("Sensors"),
                LocalizedText.english("Sensors")
        );
        
        getNodeManager().addNode(sensorsFolder);
        sensorsFolder.addReference(new Reference(
                sensorsFolderId,
                Identifiers.Organizes,
                devicesFolderId.expanded(),
                false
        ));
        
        // 创建变量节点
        createVariableNodes(sensorsFolderId);
        
        // 启动数据更新任务
        startDataUpdateTask();
        
        logger.info("OPC UA命名空间已启动");
    }
    
    @Override
    protected void onShutdown() {
        scheduledExecutor.shutdown();
        subscriptionModel.shutdown();
        super.onShutdown();
        logger.info("OPC UA命名空间已关闭");
    }
    
    /**
     * 创建变量节点
     */
    private void createVariableNodes(NodeId parentNodeId) {
        // 温度节点
        temperatureNode = createVariableNode(
                "Temperature",
                "温度",
                parentNodeId,
                25.0
        );
        
        // 压力节点
        pressureNode = createVariableNode(
                "Pressure",
                "压力",
                parentNodeId,
                5.0
        );
        
        // 流量节点
        flowNode = createVariableNode(
                "Flow",
                "流量",
                parentNodeId,
                50.0
        );
        
        // 电机状态节点
        motorStatusNode = createVariableNode(
                "MotorStatus",
                "电机状态",
                parentNodeId,
                false
        );
        
        // 计数器节点
        counterNode = createVariableNode(
                "Counter",
                "计数器",
                parentNodeId,
                0
        );
    }
    
    /**
     * 创建单个变量节点
     */
    private UaVariableNode createVariableNode(String name, String displayName, NodeId parentNodeId, Object initialValue) {
        NodeId nodeId = newNodeId(name);
        
        UaVariableNode node = new UaVariableNode.UaVariableNodeBuilder(getNodeContext())
                .setNodeId(nodeId)
                .setAccessLevel(3) // 读写
                .setBrowseName(newQualifiedName(name))
                .setDisplayName(LocalizedText.english(displayName))
                .setDataType(getDataTypeForValue(initialValue))
                .setTypeDefinition(Identifiers.BaseDataVariableType)
                .build();
        
        node.setValue(new DataValue(new Variant(initialValue)));
        
        getNodeManager().addNode(node);
        
        // 添加到父节点
        node.addReference(new Reference(
                nodeId,
                Identifiers.Organizes,
                parentNodeId.expanded(),
                false
        ));
        
        return node;
    }
    
    /**
     * 根据值类型获取数据类型
     */
    private NodeId getDataTypeForValue(Object value) {
        if (value instanceof Double || value instanceof Float) {
            return Identifiers.Double;
        } else if (value instanceof Integer) {
            return Identifiers.Int32;
        } else if (value instanceof Boolean) {
            return Identifiers.Boolean;
        } else {
            return Identifiers.String;
        }
    }
    
    /**
     * 启动数据更新任务
     */
    private void startDataUpdateTask() {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                updateNodeValues();
            } catch (Exception e) {
                logger.error("更新节点值时发生错误", e);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }
    
    /**
     * 更新节点值
     */
    private void updateNodeValues() {
        if (dataSimulator.isRunning()) {
            temperatureNode.setValue(new DataValue(new Variant(dataSimulator.getTemperature())));
            pressureNode.setValue(new DataValue(new Variant(dataSimulator.getPressure())));
            flowNode.setValue(new DataValue(new Variant(dataSimulator.getFlow())));
            motorStatusNode.setValue(new DataValue(new Variant(dataSimulator.getMotorStatus())));
            counterNode.setValue(new DataValue(new Variant(dataSimulator.getCounter())));
        }
    }
    
    @Override
    public void onDataItemsCreated(List<DataItem> dataItems) {
        subscriptionModel.onDataItemsCreated(dataItems);
    }
    
    @Override
    public void onDataItemsModified(List<DataItem> dataItems) {
        subscriptionModel.onDataItemsModified(dataItems);
    }
    
    @Override
    public void onDataItemsDeleted(List<DataItem> dataItems) {
        subscriptionModel.onDataItemsDeleted(dataItems);
    }
    
    @Override
    public void onMonitoringModeChanged(List<MonitoredItem> monitoredItems) {
        subscriptionModel.onMonitoringModeChanged(monitoredItems);
    }
}
