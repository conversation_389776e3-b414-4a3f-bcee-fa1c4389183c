package com.nimblex.iot.protocol;

/**
 * 支持的协议类型枚举
 */
public enum ProtocolType {
    OPC_UA("OPC UA", "OPC统一架构协议"),
    MODBUS_TCP("Modbus TCP", "Modbus TCP协议"),
    MODBUS_RTU("Modbus RTU", "Modbus RTU协议"),
    OPC_DA("OPC DA", "OPC数据访问协议");
    
    private final String name;
    private final String description;
    
    ProtocolType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name + " (" + description + ")";
    }
}
