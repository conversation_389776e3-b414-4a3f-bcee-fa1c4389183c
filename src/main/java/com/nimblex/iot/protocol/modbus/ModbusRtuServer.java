package com.nimblex.iot.protocol.modbus;

import com.nimblex.iot.data.DataSimulator;
import com.nimblex.iot.protocol.ProtocolServer;
import com.nimblex.iot.protocol.ProtocolType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Modbus RTU 服务器实现（简化版本）
 * 注意：这是一个简化的实现，实际项目中需要完整的Modbus RTU协议栈
 */
public class ModbusRtuServer implements ProtocolServer {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private DataSimulator dataSimulator;
    private ExecutorService executorService;
    private ScheduledExecutorService scheduledExecutor;
    private final AtomicBoolean running = new AtomicBoolean(false);

    private final String portName;
    private final int baudRate;
    private final int dataBits;
    private final int stopBits;
    private final int parity;
    private final int unitId;
    private final long updateInterval;

    public ModbusRtuServer(String portName, int baudRate, int dataBits, int stopBits,
                          int parity, int unitId, long updateInterval) {
        this.portName = portName;
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
        this.unitId = unitId;
        this.updateInterval = updateInterval;
        this.dataSimulator = new DataSimulator();
    }

    @Override
    public void start() throws Exception {
        logger.info("启动Modbus RTU服务器（简化版本）...");

        running.set(true);

        // 启动虚拟RTU服务器（用于演示）
        startVirtualRtuServer();

        logger.info("Modbus RTU服务器已启动，串口: {}, 波特率: {}, 单元ID: {}",
                   portName, baudRate, unitId);
    }

    @Override
    public void stop() throws Exception {
        logger.info("停止Modbus RTU服务器...");

        running.set(false);

        if (dataSimulator != null) {
            dataSimulator.stop();
        }

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
        }

        if (executorService != null) {
            executorService.shutdown();
        }

        logger.info("Modbus RTU服务器已停止");
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public ProtocolType getProtocolType() {
        return ProtocolType.MODBUS_RTU;
    }

    @Override
    public String getServerInfo() {
        return String.format("Modbus RTU服务器 - 串口: %s, 波特率: %d, 单元ID: %d",
                           portName, baudRate, unitId);
    }

    /**
     * 启动虚拟RTU服务器（用于演示）
     */
    private void startVirtualRtuServer() {
        executorService = Executors.newSingleThreadExecutor();
        running.set(true);

        // 启动虚拟数据处理
        executorService.submit(() -> {
            while (running.get()) {
                try {
                    // 模拟处理Modbus RTU请求
                    Thread.sleep(100);

                    if (dataSimulator.isRunning()) {
                        // 模拟日志输出，显示当前数据
                        if (System.currentTimeMillis() % 5000 < 100) { // 每5秒输出一次
                            logger.info("虚拟Modbus RTU数据 - 温度: {:.1f}°C, 压力: {:.2f}bar, 流量: {:.1f}L/min",
                                       dataSimulator.getTemperature(),
                                       dataSimulator.getPressure(),
                                       dataSimulator.getFlow());
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });

        // 启动数据模拟器
        dataSimulator.start(updateInterval);

        logger.info("虚拟Modbus RTU服务器已启动（演示模式）");
    }

    /**
     * 处理串口数据
     */
    private void processSerialData() {
        byte[] buffer = new byte[256];

        while (running.get() && serialPort.isOpen()) {
            try {
                int bytesRead = serialPort.readBytes(buffer, buffer.length);

                if (bytesRead > 0) {
                    // 处理接收到的Modbus RTU请求
                    processModbusRtuRequest(buffer, bytesRead);
                }

                Thread.sleep(10); // 短暂休眠避免CPU占用过高
            } catch (Exception e) {
                logger.error("处理串口数据时发生错误", e);
                break;
            }
        }
    }

    /**
     * 处理Modbus RTU请求
     * 注意：这是一个简化的实现，实际项目中需要完整的协议解析
     */
    private void processModbusRtuRequest(byte[] data, int length) {
        if (length < 4) {
            return; // 数据太短，不是有效的Modbus请求
        }

        int slaveId = data[0] & 0xFF;
        int functionCode = data[1] & 0xFF;

        // 检查是否是发给本设备的请求
        if (slaveId != unitId) {
            return;
        }

        logger.debug("收到Modbus RTU请求: 从站ID={}, 功能码={}", slaveId, functionCode);

        // 根据功能码处理请求并发送响应
        byte[] response = createModbusResponse(functionCode, data, length);
        if (response != null) {
            serialPort.writeBytes(response, response.length);
            logger.debug("发送Modbus RTU响应，长度: {}", response.length);
        }
    }

    /**
     * 创建Modbus响应
     * 注意：这是一个简化的实现
     */
    private byte[] createModbusResponse(int functionCode, byte[] request, int length) {
        switch (functionCode) {
            case 0x03: // 读保持寄存器
                return createReadHoldingRegistersResponse(request, length);
            case 0x04: // 读输入寄存器
                return createReadInputRegistersResponse(request, length);
            case 0x01: // 读线圈
                return createReadCoilsResponse(request, length);
            case 0x02: // 读离散输入
                return createReadDiscreteInputsResponse(request, length);
            default:
                logger.warn("不支持的功能码: {}", functionCode);
                return null;
        }
    }

    /**
     * 创建读保持寄存器响应
     */
    private byte[] createReadHoldingRegistersResponse(byte[] request, int length) {
        // 简化实现：返回模拟数据
        int startAddress = ((request[2] & 0xFF) << 8) | (request[3] & 0xFF);
        int quantity = ((request[4] & 0xFF) << 8) | (request[5] & 0xFF);

        byte[] response = new byte[3 + quantity * 2 + 2]; // 从站ID + 功能码 + 字节数 + 数据 + CRC
        response[0] = (byte) unitId;
        response[1] = 0x03;
        response[2] = (byte) (quantity * 2);

        // 填充数据
        for (int i = 0; i < quantity; i++) {
            int value = getRegisterValue(startAddress + i);
            response[3 + i * 2] = (byte) ((value >> 8) & 0xFF);
            response[3 + i * 2 + 1] = (byte) (value & 0xFF);
        }

        // 计算并添加CRC（简化实现，实际应使用正确的CRC算法）
        int crc = calculateCRC(response, response.length - 2);
        response[response.length - 2] = (byte) (crc & 0xFF);
        response[response.length - 1] = (byte) ((crc >> 8) & 0xFF);

        return response;
    }

    /**
     * 其他响应方法的简化实现
     */
    private byte[] createReadInputRegistersResponse(byte[] request, int length) {
        return createReadHoldingRegistersResponse(request, length); // 简化实现
    }

    private byte[] createReadCoilsResponse(byte[] request, int length) {
        // 简化实现
        byte[] response = new byte[6];
        response[0] = (byte) unitId;
        response[1] = 0x01;
        response[2] = 0x01;
        response[3] = (byte) (dataSimulator.getMotorStatus() ? 0x01 : 0x00);
        return response;
    }

    private byte[] createReadDiscreteInputsResponse(byte[] request, int length) {
        return createReadCoilsResponse(request, length); // 简化实现
    }

    /**
     * 获取寄存器值
     */
    private int getRegisterValue(int address) {
        switch (address) {
            case 0: return (int) (dataSimulator.getTemperature() * 10);
            case 1: return (int) (dataSimulator.getPressure() * 100);
            case 2: return (int) dataSimulator.getFlow();
            case 3: return dataSimulator.getCounter() & 0xFFFF;
            case 4: return (dataSimulator.getCounter() >> 16) & 0xFFFF;
            default: return address * 10;
        }
    }

    /**
     * 简化的CRC计算（实际项目中应使用标准的Modbus CRC算法）
     */
    private int calculateCRC(byte[] data, int length) {
        int crc = 0xFFFF;
        for (int i = 0; i < length; i++) {
            crc ^= (data[i] & 0xFF);
            for (int j = 0; j < 8; j++) {
                if ((crc & 0x0001) != 0) {
                    crc >>= 1;
                    crc ^= 0xA001;
                } else {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }
}
