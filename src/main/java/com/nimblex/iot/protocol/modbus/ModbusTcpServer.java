package com.nimblex.iot.protocol.modbus;

import com.nimblex.iot.data.DataSimulator;
import com.nimblex.iot.protocol.ProtocolServer;
import com.nimblex.iot.protocol.ProtocolType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Modbus TCP 服务器实现（简化版本）
 */
public class ModbusTcpServer implements ProtocolServer {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private ServerSocket serverSocket;
    private DataSimulator dataSimulator;
    private ExecutorService executorService;
    private ScheduledExecutorService scheduledExecutor;
    private final AtomicBoolean running = new AtomicBoolean(false);

    private final String host;
    private final int port;
    private final int unitId;
    private final long updateInterval;

    public ModbusTcpServer(String host, int port, int unitId, long updateInterval) {
        this.host = host;
        this.port = port;
        this.unitId = unitId;
        this.updateInterval = updateInterval;
        this.dataSimulator = new DataSimulator();
    }

    @Override
    public void start() throws Exception {
        logger.info("启动Modbus TCP服务器（简化版本）...");

        running.set(true);

        // 创建服务器Socket
        serverSocket = new ServerSocket(port);

        // 启动客户端处理线程
        executorService = Executors.newCachedThreadPool();
        executorService.submit(this::handleClients);

        // 启动数据模拟器
        dataSimulator.start(updateInterval);

        // 启动数据日志输出
        startDataLogging();

        logger.info("Modbus TCP服务器已启动，地址: {}:{}, 单元ID: {}", host, port, unitId);
    }

    @Override
    public void stop() throws Exception {
        logger.info("停止Modbus TCP服务器...");

        running.set(false);

        if (dataSimulator != null) {
            dataSimulator.stop();
        }

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
        }

        if (executorService != null) {
            executorService.shutdown();
        }

        if (serverSocket != null && !serverSocket.isClosed()) {
            serverSocket.close();
        }

        logger.info("Modbus TCP服务器已停止");
    }

    @Override
    public boolean isRunning() {
        return running.get() && serverSocket != null && !serverSocket.isClosed();
    }

    @Override
    public ProtocolType getProtocolType() {
        return ProtocolType.MODBUS_TCP;
    }

    @Override
    public String getServerInfo() {
        return String.format("Modbus TCP服务器（简化版） - 地址: %s:%d, 单元ID: %d", host, port, unitId);
    }

    /**
     * 处理客户端连接
     */
    private void handleClients() {
        while (running.get() && !serverSocket.isClosed()) {
            try {
                Socket clientSocket = serverSocket.accept();
                logger.info("接收到Modbus TCP客户端连接: {}", clientSocket.getRemoteSocketAddress());

                // 在实际实现中，这里应该处理Modbus TCP协议
                // 这里只是简单模拟
                executorService.submit(() -> handleClient(clientSocket));

            } catch (IOException e) {
                if (running.get()) {
                    logger.error("接收客户端连接时发生错误", e);
                }
            }
        }
    }

    /**
     * 处理单个客户端
     */
    private void handleClient(Socket clientSocket) {
        try {
            // 简化实现：只是保持连接一段时间然后关闭
            Thread.sleep(5000);
            clientSocket.close();
            logger.info("客户端连接已关闭: {}", clientSocket.getRemoteSocketAddress());
        } catch (Exception e) {
            logger.error("处理客户端连接时发生错误", e);
        }
    }

    /**
     * 启动数据日志输出
     */
    private void startDataLogging() {
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutor.scheduleAtFixedRate(() -> {
            if (dataSimulator.isRunning()) {
                logger.info("Modbus TCP数据 - 温度: {:.1f}°C, 压力: {:.2f}bar, 流量: {:.1f}L/min, 电机: {}, 计数: {}",
                           dataSimulator.getTemperature(),
                           dataSimulator.getPressure(),
                           dataSimulator.getFlow(),
                           dataSimulator.getMotorStatus() ? "运行" : "停止",
                           dataSimulator.getCounter());
            }
        }, 5, 5, TimeUnit.SECONDS);
    }

}
