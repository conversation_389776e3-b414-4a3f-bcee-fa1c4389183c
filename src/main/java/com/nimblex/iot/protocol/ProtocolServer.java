package com.nimblex.iot.protocol;

/**
 * 协议服务器接口
 * 定义了所有协议服务器的通用方法
 */
public interface ProtocolServer {
    
    /**
     * 启动服务器
     * @throws Exception 启动异常
     */
    void start() throws Exception;
    
    /**
     * 停止服务器
     * @throws Exception 停止异常
     */
    void stop() throws Exception;
    
    /**
     * 检查服务器是否正在运行
     * @return true如果正在运行，否则false
     */
    boolean isRunning();
    
    /**
     * 获取协议类型
     * @return 协议类型
     */
    ProtocolType getProtocolType();
    
    /**
     * 获取服务器信息
     * @return 服务器信息字符串
     */
    String getServerInfo();
}
