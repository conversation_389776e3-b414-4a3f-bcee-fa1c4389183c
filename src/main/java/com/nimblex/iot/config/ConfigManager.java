package com.nimblex.iot.config;

import com.nimblex.iot.protocol.ProtocolType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 配置管理器
 * 负责加载和管理应用程序配置
 */
public class ConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static final String CONFIG_FILE = "application.properties";
    
    private final Properties properties;
    
    public ConfigManager() {
        this.properties = new Properties();
        loadConfiguration();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                logger.warn("配置文件 {} 未找到，使用默认配置", CONFIG_FILE);
                loadDefaultConfiguration();
                return;
            }
            
            properties.load(inputStream);
            logger.info("成功加载配置文件: {}", CONFIG_FILE);
            
        } catch (IOException e) {
            logger.error("加载配置文件失败: {}", CONFIG_FILE, e);
            loadDefaultConfiguration();
        }
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfiguration() {
        properties.setProperty("protocol.type", "OPC_UA");
        properties.setProperty("opcua.server.port", "4840");
        properties.setProperty("opcua.server.endpoint", "opc.tcp://localhost:4840/iot-gateway");
        properties.setProperty("opcua.server.name", "IoT Gateway OPC UA Server");
        properties.setProperty("opcua.data.update.interval", "1000");
        
        properties.setProperty("modbus.tcp.port", "502");
        properties.setProperty("modbus.tcp.host", "localhost");
        properties.setProperty("modbus.tcp.unit.id", "1");
        properties.setProperty("modbus.tcp.data.update.interval", "1000");
        
        properties.setProperty("modbus.rtu.port", "COM1");
        properties.setProperty("modbus.rtu.baudrate", "9600");
        properties.setProperty("modbus.rtu.databits", "8");
        properties.setProperty("modbus.rtu.stopbits", "1");
        properties.setProperty("modbus.rtu.parity", "NONE");
        properties.setProperty("modbus.rtu.unit.id", "1");
        properties.setProperty("modbus.rtu.data.update.interval", "1000");
        
        properties.setProperty("opcda.server.name", "IoT Gateway OPC DA Server");
        properties.setProperty("opcda.server.clsid", "{12345678-1234-1234-1234-123456789ABC}");
        properties.setProperty("opcda.data.update.interval", "1000");
        
        properties.setProperty("data.simulation.enabled", "true");
        properties.setProperty("data.simulation.temperature.min", "20.0");
        properties.setProperty("data.simulation.temperature.max", "80.0");
        properties.setProperty("data.simulation.pressure.min", "1.0");
        properties.setProperty("data.simulation.pressure.max", "10.0");
        properties.setProperty("data.simulation.flow.min", "0.0");
        properties.setProperty("data.simulation.flow.max", "100.0");
        
        properties.setProperty("logging.level", "INFO");
        
        logger.info("已加载默认配置");
    }
    
    /**
     * 获取协议类型
     */
    public ProtocolType getProtocolType() {
        String protocolTypeStr = properties.getProperty("protocol.type", "OPC_UA");
        try {
            return ProtocolType.valueOf(protocolTypeStr);
        } catch (IllegalArgumentException e) {
            logger.warn("无效的协议类型配置: {}, 使用默认值 OPC_UA", protocolTypeStr);
            return ProtocolType.OPC_UA;
        }
    }
    
    /**
     * 获取所有属性
     */
    public Properties getProperties() {
        return new Properties(properties);
    }
    
    /**
     * 获取属性值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取属性值（带默认值）
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整数属性值
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("无效的整数配置 {}: {}, 使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取长整数属性值
     */
    public long getLongProperty(String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            logger.warn("无效的长整数配置 {}: {}, 使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取双精度浮点数属性值
     */
    public double getDoubleProperty(String key, double defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            logger.warn("无效的浮点数配置 {}: {}, 使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔属性值
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 打印配置信息
     */
    public void printConfiguration() {
        logger.info("当前配置信息:");
        logger.info("  协议类型: {}", getProtocolType());
        
        switch (getProtocolType()) {
            case OPC_UA:
                logger.info("  OPC UA配置:");
                logger.info("    端口: {}", getProperty("opcua.server.port"));
                logger.info("    端点: {}", getProperty("opcua.server.endpoint"));
                logger.info("    服务器名: {}", getProperty("opcua.server.name"));
                logger.info("    更新间隔: {}ms", getProperty("opcua.data.update.interval"));
                break;
                
            case MODBUS_TCP:
                logger.info("  Modbus TCP配置:");
                logger.info("    地址: {}:{}", getProperty("modbus.tcp.host"), getProperty("modbus.tcp.port"));
                logger.info("    单元ID: {}", getProperty("modbus.tcp.unit.id"));
                logger.info("    更新间隔: {}ms", getProperty("modbus.tcp.data.update.interval"));
                break;
                
            case MODBUS_RTU:
                logger.info("  Modbus RTU配置:");
                logger.info("    串口: {}", getProperty("modbus.rtu.port"));
                logger.info("    波特率: {}", getProperty("modbus.rtu.baudrate"));
                logger.info("    数据位: {}", getProperty("modbus.rtu.databits"));
                logger.info("    停止位: {}", getProperty("modbus.rtu.stopbits"));
                logger.info("    校验位: {}", getProperty("modbus.rtu.parity"));
                logger.info("    单元ID: {}", getProperty("modbus.rtu.unit.id"));
                logger.info("    更新间隔: {}ms", getProperty("modbus.rtu.data.update.interval"));
                break;
                
            case OPC_DA:
                logger.info("  OPC DA配置:");
                logger.info("    服务器名: {}", getProperty("opcda.server.name"));
                logger.info("    CLSID: {}", getProperty("opcda.server.clsid"));
                logger.info("    更新间隔: {}ms", getProperty("opcda.data.update.interval"));
                break;
        }
        
        logger.info("  数据模拟配置:");
        logger.info("    启用: {}", getProperty("data.simulation.enabled"));
        logger.info("    温度范围: {} - {}°C", 
                   getProperty("data.simulation.temperature.min"),
                   getProperty("data.simulation.temperature.max"));
        logger.info("    压力范围: {} - {}bar", 
                   getProperty("data.simulation.pressure.min"),
                   getProperty("data.simulation.pressure.max"));
        logger.info("    流量范围: {} - {}L/min", 
                   getProperty("data.simulation.flow.min"),
                   getProperty("data.simulation.flow.max"));
    }
}
