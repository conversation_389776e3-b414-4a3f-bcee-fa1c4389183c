# ???????
# ???????: OPC_UA, MODBUS_TCP, MODBUS_RTU, OPC_DA
protocol.type=OPC_UA

# OPC UA ??
opcua.server.port=4840
opcua.server.endpoint=opc.tcp://localhost:4840/iot-gateway
opcua.server.name=IoT Gateway OPC UA Server
opcua.data.update.interval=1000

# Modbus TCP ??
modbus.tcp.port=502
modbus.tcp.host=localhost
modbus.tcp.unit.id=1
modbus.tcp.data.update.interval=1000

# Modbus RTU ??
modbus.rtu.port=COM1
modbus.rtu.baudrate=9600
modbus.rtu.databits=8
modbus.rtu.stopbits=1
modbus.rtu.parity=NONE
modbus.rtu.unit.id=1
modbus.rtu.data.update.interval=1000

# OPC DA ?? (????)
opcda.server.name=IoT Gateway OPC DA Server
opcda.server.clsid={12345678-1234-1234-1234-123456789ABC}
opcda.data.update.interval=1000

# ??????
data.simulation.enabled=true
data.simulation.temperature.min=20.0
data.simulation.temperature.max=80.0
data.simulation.pressure.min=1.0
data.simulation.pressure.max=10.0
data.simulation.flow.min=0.0
data.simulation.flow.max=100.0

# ????
logging.level=INFO
